esphome:
    name: 'outside-monitor-box'
    friendly_name: 'Outside Monitor Box'
    name_add_mac_suffix: true
    platformio_options:
        board_build.flash_mode: dio

esp32:
    board: esp32-c3-devkitm-1
    framework:
        type: esp-idf

web_server: # TODO: Remove in production
    version: 3

logger:
    level: DEBUG

debug:
    update_interval: 5s

api:

ota:
    - platform: esphome

sensor:
    - platform: internal_temperature
      name: 'ESP Temperature'
      entity_category: diagnostic
      icon: 'mdi:fire-alert'
      update_interval: 5s

text_sensor:
    - platform: uptime
      name: 'ESP Uptime'
      entity_category: diagnostic
      icon: 'mdi:timer-outline'
      update_interval: 60s
      format:
          separator: ' '

    - platform: debug
      reset_reason:
          name: 'ESP Reset Reason'
          entity_category: diagnostic
          icon: 'mdi:restart-alert'

button:
    - platform: restart
      id: reboot_button
      name: 'ESP Reboot'
      entity_category: diagnostic
      icon: 'mdi:restart'
      disabled_by_default: true

    - platform: factory_reset
      id: factory_reset_button
      name: 'ESP Factory Reset'
      entity_category: diagnostic
      icon: 'mdi:factory'
      disabled_by_default: true
